/**
 * @file 响应式样式文件
 * @description 移动端、平板设备、触摸设备优化
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 平板设备 */
@media (max-width: 1024px) {
    .container {
        padding: 15px;
    }

    .grid {
        gap: 20px;
    }

    #document-preview {
        --preview-scale-factor: 0.65;
    }

    #preview-container {
        /* 重新计算平板设备的容器尺寸 */
        min-height: calc(var(--a4-height-px) * 0.65 + 80px);
        min-width: calc(var(--a4-width-px) * 0.65 + 60px);
    }

    /* 平板设备下的AI填充组件优化 */
    .ai-fill-container {
        padding: 10px;
        margin-bottom: 15px;
    }

    .ai-fill-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    /* 平板设备下的按钮组优化 */
    .btn-group {
        flex-wrap: wrap;
        gap: 8px;
    }

    .export-btn {
        min-width: 100px;
        font-size: 13px;
    }
}
/* #endregion */

/* #region 小屏幕设备 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-section,
    .preview-section {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 16px;
    }

    #document-preview {
        --preview-scale-factor: 0.5;
    }

    #preview-container {
        /* 小屏幕设备的容器尺寸调整 */
        min-height: calc(var(--a4-height-px) * 0.5 + 80px);
        min-width: calc(var(--a4-width-px) * 0.5 + 60px);
        padding: 15px;
    }

    /* 小屏幕设备下的表格优化 */
    .items-table {
        font-size: 12px;
    }

    .items-table th,
    .items-table td {
        padding: 6px 4px;
    }

    .items-table input {
        font-size: 12px;
        padding: 4px;
    }

    /* 小屏幕设备下的AI填充优化 */
    .ai-fill-container {
        padding: 10px;
        margin-bottom: 15px;
    }

    .ai-fill-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .ai-fill-title {
        font-size: 13px;
    }

    #ai-text-input {
        font-size: 14px;
        min-height: 80px;
    }

    .btn-group .btn {
        flex: 1;
        font-size: 13px;
    }

    /* 小屏幕设备下的多订单管理器优化 */
    .order-manager {
        flex-direction: column;
        gap: 10px;
    }

    .order-tabs {
        flex-wrap: wrap;
    }

    .current-order-info span {
        display: block;
        margin-bottom: 5px;
    }

    /* 小屏幕设备下的预览控制优化 */
    .preview-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .export-method-selector {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
/* #endregion */

/* #region 移动设备 */
@media (max-width: 480px) {
    .container {
        padding: 5px;
    }

    .form-section,
    .preview-section {
        padding: 15px;
    }

    .items-table {
        font-size: 12px;
    }

    .items-table th,
    .items-table td {
        padding: 6px 4px;
    }

    .items-table input {
        font-size: 12px;
        padding: 4px;
    }

    .btn {
        font-size: 14px;
        padding: 10px 15px;
    }

    #document-preview {
        --preview-scale-factor: 0.4;
    }

    #preview-container {
        /* 移动设备的容器尺寸调整 */
        min-height: calc(var(--a4-height-px) * 0.4 + 80px);
        min-width: calc(var(--a4-width-px) * 0.4 + 60px);
        padding: 10px;
    }

    h1 {
        font-size: 24px;
    }

    h2 {
        font-size: 20px;
    }

    /* 移动设备下的表单优化 */
    .form-group label {
        font-size: 14px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 12px;
    }

    /* 移动设备下的总金额显示优化 */
    .total-amount-display {
        font-size: 16px;
        padding: 12px;
    }

    .total-amount-container {
        min-width: 150px;
        padding: 10px 15px;
    }

    .total-amount-container h3 {
        font-size: 14px;
    }

    /* 移动设备下的内容边距调整 */
    :root {
        --content-margin-left: 10px;
        --content-margin-right: 10px;
    }

    /* 移动设备下的预览容器标识 */
    #preview-container::before {
        font-size: 10px;
        padding: 1px 6px;
    }
}
/* #endregion */

/* #region 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        font-size: 16px;
    }

    input, select, textarea {
        min-height: 44px;
        font-size: 16px;
    }

    .items-table input {
        min-height: 40px;
    }

    /* 触摸设备下的按钮间距优化 */
    .btn-group {
        gap: 12px;
    }

    /* 触摸设备下的表格优化 */
    .items-table th,
    .items-table td {
        padding: 10px 8px;
    }

    /* 触摸设备下的AI填充优化 */
    .ai-image-input {
        min-height: 44px;
        padding: 12px;
    }

    /* 触摸设备下的订单标签优化 */
    .order-tab {
        min-height: 44px;
        padding: 12px 16px;
    }

    /* 触摸设备下的预览缩放控制 */
    .zoom-btn {
        width: 44px;
        height: 44px;
        font-size: 16px;
    }
}
/* #endregion */

/* #region 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 高分辨率屏幕下的图片优化 */
    img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }

    /* 高分辨率屏幕下的字体优化 */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
/* #endregion */

/* #region 横屏模式优化 */
@media (orientation: landscape) and (max-width: 1024px) {
    .grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    #document-preview {
        --preview-scale-factor: 0.6;
    }

    #preview-container {
        min-height: calc(var(--a4-height-px) * 0.6 + 60px);
        min-width: calc(var(--a4-width-px) * 0.6 + 40px);
    }
}
/* #endregion */

/* #region 竖屏模式优化 */
@media (orientation: portrait) and (max-width: 768px) {
    .grid {
        grid-template-columns: 1fr;
    }

    .preview-section {
        order: -1; /* 在移动设备竖屏模式下，预览区域显示在表单上方 */
    }
}
/* #endregion */

/* #region 可访问性优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }

    .preview-refreshing {
        animation: none !important;
    }

    .loading {
        animation: none !important;
    }
}

@media (prefers-color-scheme: dark) {
    /* 暗色模式下保持文档预览为白色背景 */
    #document-preview,
    #document-container {
        background: white !important;
        color: black !important;
    }
}
/* #endregion */

/* #region 打印设备响应式 */
@media print and (max-width: 210mm) {
    #document-container {
        padding: 10mm 15mm !important;
    }

    .items-table {
        font-size: 10pt !important;
    }

    .total-amount-container {
        font-size: 12pt !important;
        padding: 6px 10px !important;
    }
}
/* #endregion */
