<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片显示问题修复测试 - 更新版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            color: #1e40af;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 5px;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-description {
            background: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #28a745;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .test-result {
            background: #e7f3ff;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .header-test {
            width: 794px;
            height: 150px;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            background: #fafafa;
            margin: 10px 0;
        }
        
        .header-test img {
            height: 100%;
            width: auto;
            max-width: 100%;
            object-fit: contain;
            object-position: center;
            margin: 0 auto;
            display: block;
            /* 关键修复属性 */
            min-width: 0;
            min-height: 0;
            flex-shrink: 0;
        }
        
        .content-test {
            width: 794px;
            border: 2px solid #ddd;
            background: #fafafa;
            margin: 10px 0;
            padding: 0;
        }
        
        .content-test .items-table {
            width: calc(100% - 30px);
            margin-left: 15px;
            margin-right: 15px;
            border-collapse: collapse;
        }
        
        .content-test .notes-section {
            padding-left: 15px;
            padding-right: 15px;
            margin-left: 15px;
            margin-right: 15px;
            width: calc(100% - 30px);
            box-sizing: border-box;
        }
        
        .content-test .company-info,
        .content-test .customer-info,
        .content-test .payment-info {
            padding-left: 15px;
            padding-right: 15px;
            box-sizing: border-box;
        }
        
        .stamp-test {
            width: 96px;
            height: 96px;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: visible;
            background: #fafafa;
            margin: 10px 0;
            position: relative;
        }
        
        .stamp-test img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center;
            display: block;
            margin: 0;
            padding: 0;
            opacity: 0.9;
        }
        
        .export-mode .header-test {
            position: relative;
            width: 100%;
            height: 150px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .export-mode .header-test img {
            height: 100%;
            width: auto;
            max-width: 100%;
            object-fit: contain;
            object-position: center;
            margin: 0 auto;
            display: block;
            border: none;
            background: transparent;
            padding: 0;
            box-sizing: border-box;
            /* 强制防止拉伸变形 - 关键修复 */
            min-width: 0;
            min-height: 0;
            flex-shrink: 0;
        }
        
        .export-mode .stamp-test {
            position: absolute;
            bottom: 15%;
            right: calc(5% + 15px);
            z-index: 30;
            width: 96px;
            height: 96px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: visible;
            box-sizing: border-box;
        }
        
        .export-mode .stamp-test img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            object-position: center;
            display: block;
            margin: 0;
            padding: 0;
            border: none;
            background: transparent;
            opacity: 0.9;
        }
        
        .test-button {
            background: #1e40af;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #1d4ed8;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass {
            background: #28a745;
        }
        
        .status-fail {
            background: #dc3545;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .image-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .margin-demo {
            border: 1px solid #ccc;
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
        }
        
        .margin-demo table {
            width: calc(100% - 30px);
            margin-left: 15px;
            margin-right: 15px;
            border-collapse: collapse;
            border: 1px solid #333;
        }
        
        .margin-demo table td,
        .margin-demo table th {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        
        .margin-demo .notes {
            padding-left: 15px;
            padding-right: 15px;
            margin: 10px 0;
            background: #fff;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">🔧 发票/收据生成器图片显示问题修复测试 - 更新版</div>
        
        <div class="test-section">
            <h3>问题1: 页眉图片拉伸变形修复测试</h3>
            <div class="test-description">
                <strong>修复内容:</strong> 确保页眉图片在导出时保持原始宽高比，使用object-fit: contain属性，在固定高度150px的容器内居中显示，宽度自适应。<br>
                <strong>关键修复:</strong> 添加了min-width: 0、min-height: 0、flex-shrink: 0等关键属性防止拉伸变形。
            </div>
            
            <div class="header-test" id="header-test">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzFlNDBhZiIvPgogIDx0ZXh0IHg9IjIwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkdvTXlIaXJlPC90ZXh0Pgo8L3N2Zz4K" alt="页眉图片测试">
            </div>
            
            <div class="image-info" id="header-info">
                图片信息: 400x100px (4:1 宽高比) → 容器: 794x150px
            </div>
            
            <button class="test-button" onclick="testHeaderImage()">测试页眉图片显示</button>
            <button class="test-button" onclick="toggleExportMode()">切换导出模式</button>
            
            <div class="test-result" id="header-result">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <h3>问题2: 文字内容边距修复测试</h3>
            <div class="test-description">
                <strong>修复内容:</strong> 为发票/收据的文字内容添加左右边距（15px），避免内容紧贴页面边缘。<br>
                <strong>影响区域:</strong> 项目详情表格、备注部分、公司信息、客户信息、总金额容器等。
            </div>
            
            <div class="margin-demo">
                <h4>边距演示</h4>
                <table>
                    <tr>
                        <th>项目</th>
                        <th>数量</th>
                        <th>单价</th>
                        <th>金额</th>
                    </tr>
                    <tr>
                        <td>测试项目</td>
                        <td>1</td>
                        <td>RM 100.00</td>
                        <td>RM 100.00</td>
                    </tr>
                </table>
                
                <div class="notes">
                    <strong>备注:</strong> 这是一个测试备注，用于验证左右边距是否正确应用。文字内容应该有15px的左右边距，不会紧贴页面边缘。
                </div>
            </div>
            
            <button class="test-button" onclick="testContentMargins()">测试内容边距</button>
            
            <div class="test-result" id="margin-result">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <h3>问题3: 印章图片被裁切修复测试</h3>
            <div class="test-description">
                <strong>修复内容:</strong> 印章图片自动适应96x96px容器大小，确保图片完整可见，容器overflow设置为visible防止裁切。
            </div>
            
            <div class="stamp-test" id="stamp-test">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZGMzNTQ1IiBzdHJva2Utd2lkdGg9IjMiLz4KICA8dGV4dCB4PSI1MCIgeT0iNDAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2RjMzU0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+S0tQTjwvdGV4dD4KICA8dGV4dCB4PSI1MCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2RjMzU0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+OTUyNjwvdGV4dD4KPC9zdmc+Cg==" alt="印章图片测试">
            </div>
            
            <div class="image-info" id="stamp-info">
                图片信息: 100x100px (1:1 宽高比) → 容器: 96x96px，透明度: 0.9
            </div>
            
            <button class="test-button" onclick="testStampImage()">测试印章图片显示</button>
            
            <div class="test-result" id="stamp-result">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
    </div>

    <script>
        let isExportMode = false;
        
        function testHeaderImage() {
            const img = document.querySelector('#header-test img');
            const container = document.getElementById('header-test');
            const result = document.getElementById('header-result');
            
            const imgNaturalWidth = img.naturalWidth;
            const imgNaturalHeight = img.naturalHeight;
            const imgDisplayWidth = img.offsetWidth;
            const imgDisplayHeight = img.offsetHeight;
            const containerWidth = container.offsetWidth;
            const containerHeight = container.offsetHeight;
            
            const aspectRatioOriginal = imgNaturalWidth / imgNaturalHeight;
            const aspectRatioDisplay = imgDisplayWidth / imgDisplayHeight;
            const aspectRatioMaintained = Math.abs(aspectRatioOriginal - aspectRatioDisplay) < 0.1;
            
            const isProperlyContained = imgDisplayHeight <= containerHeight && imgDisplayWidth <= containerWidth;
            const isNotStretched = aspectRatioMaintained;
            
            // 检查关键修复属性
            const imgStyle = window.getComputedStyle(img);
            const hasMinWidth = imgStyle.minWidth === '0px';
            const hasMinHeight = imgStyle.minHeight === '0px';
            const hasFlexShrink = imgStyle.flexShrink === '0';
            
            let status = 'status-pass';
            let message = '✅ 页眉图片显示正常，防拉伸修复生效';
            
            if (!isProperlyContained) {
                status = 'status-fail';
                message = '❌ 图片超出容器边界';
            } else if (!isNotStretched) {
                status = 'status-fail';
                message = '❌ 图片宽高比被拉伸变形';
            } else if (!hasMinWidth || !hasMinHeight || !hasFlexShrink) {
                status = 'status-warning';
                message = '⚠️ 防拉伸属性未完全应用';
            }
            
            result.innerHTML = `
                <span class="status-indicator ${status}"></span>
                ${message}<br>
                <small>
                    原始尺寸: ${imgNaturalWidth}x${imgNaturalHeight}px (${aspectRatioOriginal.toFixed(2)}:1)<br>
                    显示尺寸: ${imgDisplayWidth}x${imgDisplayHeight}px (${aspectRatioDisplay.toFixed(2)}:1)<br>
                    容器尺寸: ${containerWidth}x${containerHeight}px<br>
                    宽高比保持: ${aspectRatioMaintained ? '是' : '否'}<br>
                    防拉伸属性: min-width:${hasMinWidth?'✓':'✗'} min-height:${hasMinHeight?'✓':'✗'} flex-shrink:${hasFlexShrink?'✓':'✗'}
                </small>
            `;
        }
        
        function testContentMargins() {
            const result = document.getElementById('margin-result');
            const table = document.querySelector('.margin-demo table');
            const notes = document.querySelector('.margin-demo .notes');
            
            const tableStyle = window.getComputedStyle(table);
            const notesStyle = window.getComputedStyle(notes);
            
            const tableMarginLeft = parseInt(tableStyle.marginLeft);
            const tableMarginRight = parseInt(tableStyle.marginRight);
            const notesPaddingLeft = parseInt(notesStyle.paddingLeft);
            const notesPaddingRight = parseInt(notesStyle.paddingRight);
            
            const expectedMargin = 15;
            const tableMarginCorrect = tableMarginLeft === expectedMargin && tableMarginRight === expectedMargin;
            const notesPaddingCorrect = notesPaddingLeft === expectedMargin && notesPaddingRight === expectedMargin;
            
            let status = 'status-pass';
            let message = '✅ 内容边距设置正确';
            
            if (!tableMarginCorrect && !notesPaddingCorrect) {
                status = 'status-fail';
                message = '❌ 表格和备注边距都不正确';
            } else if (!tableMarginCorrect) {
                status = 'status-warning';
                message = '⚠️ 表格边距不正确';
            } else if (!notesPaddingCorrect) {
                status = 'status-warning';
                message = '⚠️ 备注边距不正确';
            }
            
            result.innerHTML = `
                <span class="status-indicator ${status}"></span>
                ${message}<br>
                <small>
                    表格边距: 左${tableMarginLeft}px 右${tableMarginRight}px (期望: ${expectedMargin}px)<br>
                    备注边距: 左${notesPaddingLeft}px 右${notesPaddingRight}px (期望: ${expectedMargin}px)<br>
                    边距修复: ${tableMarginCorrect && notesPaddingCorrect ? '完全生效' : '部分生效'}
                </small>
            `;
        }
        
        function testStampImage() {
            const img = document.querySelector('#stamp-test img');
            const container = document.getElementById('stamp-test');
            const result = document.getElementById('stamp-result');
            
            const imgNaturalWidth = img.naturalWidth;
            const imgNaturalHeight = img.naturalHeight;
            const imgDisplayWidth = img.offsetWidth;
            const imgDisplayHeight = img.offsetHeight;
            const containerWidth = container.offsetWidth;
            const containerHeight = container.offsetHeight;
            const opacity = window.getComputedStyle(img).opacity;
            const overflow = window.getComputedStyle(container).overflow;
            
            const isProperlyContained = imgDisplayHeight <= containerHeight && imgDisplayWidth <= containerWidth;
            const hasCorrectOpacity = Math.abs(parseFloat(opacity) - 0.9) < 0.1;
            const isVisible = img.offsetWidth > 0 && img.offsetHeight > 0;
            const hasVisibleOverflow = overflow === 'visible';
            
            let status = 'status-pass';
            let message = '✅ 印章图片显示正常，防裁切修复生效';
            
            if (!isVisible) {
                status = 'status-fail';
                message = '❌ 印章图片不可见或被裁切';
            } else if (!hasVisibleOverflow) {
                status = 'status-warning';
                message = '⚠️ 容器overflow设置不正确';
            } else if (!hasCorrectOpacity) {
                status = 'status-warning';
                message = '⚠️ 透明度设置不正确';
            }
            
            result.innerHTML = `
                <span class="status-indicator ${status}"></span>
                ${message}<br>
                <small>
                    原始尺寸: ${imgNaturalWidth}x${imgNaturalHeight}px<br>
                    显示尺寸: ${imgDisplayWidth}x${imgDisplayHeight}px<br>
                    容器尺寸: ${containerWidth}x${containerHeight}px<br>
                    透明度: ${opacity} (期望: 0.9)<br>
                    容器overflow: ${overflow} (期望: visible)<br>
                    可见性: ${isVisible ? '是' : '否'}
                </small>
            `;
        }
        
        function toggleExportMode() {
            const container = document.body;
            isExportMode = !isExportMode;
            
            if (isExportMode) {
                container.classList.add('export-mode');
                document.querySelector('button[onclick="toggleExportMode()"]').textContent = '退出导出模式';
            } else {
                container.classList.remove('export-mode');
                document.querySelector('button[onclick="toggleExportMode()"]').textContent = '切换导出模式';
            }
            
            // 重新测试图片显示
            setTimeout(() => {
                testHeaderImage();
                testStampImage();
                testContentMargins();
            }, 100);
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testHeaderImage();
                testContentMargins();
                testStampImage();
            }, 500);
        });
    </script>
</body>
</html>
