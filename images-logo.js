/**
 * @file images-logo.js - 公司标志图片资源管理模块
 * @description 管理各公司的标志图片base64数据和相关操作
 * @version 1.0.0
 * <AUTHOR> Receipt Generator
 */

/**
 * 公司标志图片管理器
 * @class LogoImageManager - 管理公司标志图片的base64数据
 */
class LogoImageManager {
    /**
     * 构造函数
     * @function constructor - 初始化标志图片管理器
     */
    constructor() {
        this.logos = {
            'sky-mirror': '', // Sky Mirror World Tour 标志占位符
            'gomyhire': ''    // GoMyHire Travel 标志占位符
        };
        
        // 初始化时加载默认数据
        this.initializeDefaultLogos();
    }

    /**
     * 初始化默认标志数据
     * @function initializeDefaultLogos - 加载默认的标志图片数据
     */
    initializeDefaultLogos() {
        // 这里可以添加默认的base64图片数据
        // 目前保持为空，等待用户上传或设置
        console.log('📋 标志图片管理器已初始化');
    }

    /**
     * 获取指定公司的标志图片
     * @function getLogo - 获取公司标志图片base64数据
     * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
     * @returns {string} 标志图片的base64数据，如果不存在则返回空字符串
     */
    getLogo(company) {
        if (!company || typeof company !== 'string') {
            console.warn('⚠️ 无效的公司代码:', company);
            return '';
        }

        const logo = this.logos[company.toLowerCase()];
        if (!logo) {
            console.log(`📋 公司 ${company} 的标志图片未设置`);
            return '';
        }

        return logo;
    }

    /**
     * 设置指定公司的标志图片
     * @function setLogo - 设置公司标志图片base64数据
     * @param {string} company - 公司代码 ('sky-mirror' 或 'gomyhire')
     * @param {string} base64Data - 标志图片的base64数据
     * @returns {boolean} 设置是否成功
     */
    setLogo(company, base64Data) {
        if (!company || typeof company !== 'string') {
            console.error('❌ 无效的公司代码:', company);
            return false;
        }

        if (!base64Data || typeof base64Data !== 'string') {
            console.error('❌ 无效的base64数据');
            return false;
        }

        // 验证base64数据格式
        if (!this.validateBase64Image(base64Data)) {
            console.error('❌ 无效的图片base64格式');
            return false;
        }

        this.logos[company.toLowerCase()] = base64Data;
        console.log(`✅ 已设置公司 ${company} 的标志图片`);
        
        // 触发更新事件（如果需要）
        this.notifyLogoUpdate(company);
        
        return true;
    }

    /**
     * 删除指定公司的标志图片
     * @function removeLogo - 删除公司标志图片
     * @param {string} company - 公司代码
     * @returns {boolean} 删除是否成功
     */
    removeLogo(company) {
        if (!company || typeof company !== 'string') {
            console.error('❌ 无效的公司代码:', company);
            return false;
        }

        if (this.logos.hasOwnProperty(company.toLowerCase())) {
            this.logos[company.toLowerCase()] = '';
            console.log(`✅ 已删除公司 ${company} 的标志图片`);
            this.notifyLogoUpdate(company);
            return true;
        }

        console.warn(`⚠️ 公司 ${company} 的标志图片不存在`);
        return false;
    }

    /**
     * 获取所有公司的标志图片列表
     * @function getAllLogos - 获取所有标志图片数据
     * @returns {Object} 包含所有公司标志图片的对象
     */
    getAllLogos() {
        return { ...this.logos };
    }

    /**
     * 检查指定公司是否有标志图片
     * @function hasLogo - 检查公司是否设置了标志图片
     * @param {string} company - 公司代码
     * @returns {boolean} 是否存在标志图片
     */
    hasLogo(company) {
        if (!company || typeof company !== 'string') {
            return false;
        }
        
        const logo = this.logos[company.toLowerCase()];
        return logo && logo.length > 0;
    }

    /**
     * 验证base64图片数据格式
     * @function validateBase64Image - 验证base64图片数据是否有效
     * @param {string} base64Data - base64数据
     * @returns {boolean} 是否为有效的图片base64数据
     */
    validateBase64Image(base64Data) {
        if (!base64Data || typeof base64Data !== 'string') {
            return false;
        }

        // 检查是否包含data:image前缀
        const imageRegex = /^data:image\/(jpeg|jpg|png|gif|webp|svg\+xml);base64,/i;
        return imageRegex.test(base64Data);
    }

    /**
     * 通知标志图片更新
     * @function notifyLogoUpdate - 通知系统标志图片已更新
     * @param {string} company - 更新的公司代码
     */
    notifyLogoUpdate(company) {
        // 触发自定义事件，通知其他组件标志图片已更新
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('logoUpdated', {
                detail: { company: company }
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 导出标志图片数据为JSON
     * @function exportToJSON - 导出所有标志图片数据
     * @returns {string} JSON格式的标志图片数据
     */
    exportToJSON() {
        return JSON.stringify(this.logos, null, 2);
    }

    /**
     * 从JSON导入标志图片数据
     * @function importFromJSON - 从JSON数据导入标志图片
     * @param {string} jsonData - JSON格式的标志图片数据
     * @returns {boolean} 导入是否成功
     */
    importFromJSON(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            if (typeof data === 'object' && data !== null) {
                this.logos = { ...this.logos, ...data };
                console.log('✅ 标志图片数据导入成功');
                return true;
            }
        } catch (error) {
            console.error('❌ 标志图片数据导入失败:', error);
        }
        return false;
    }
}

// 创建全局实例
const logoImageManager = new LogoImageManager();

// 导出模块（支持不同的模块系统）
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = { LogoImageManager, logoImageManager };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.LogoImageManager = LogoImageManager;
    window.logoImageManager = logoImageManager;
}

console.log('🎨 标志图片管理模块已加载');
