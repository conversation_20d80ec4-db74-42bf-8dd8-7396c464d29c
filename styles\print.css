/**
 * @file 打印样式文件
 * @description 打印专用样式，修复object-fit冲突，确保打印时图片保持正确比例
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 打印专用样式 - Print2PDF方案 */
@media print {
    /* 隐藏不需要打印的元素 */
    .form-section,
    .preview-header,
    .btn,
    .btn-group,
    #export-method-selector,
    #export-method-info,
    .preview-status-indicator,
    .container > h1,
    .grid,
    .empty-preview-message,
    .preview-zoom-controls,
    .ai-fill-container {
        display: none !important;
    }

    /* 重置页面布局为打印优化 */
    body {
        margin: 0;
        padding: 0;
        background: white;
        font-size: 12pt;
        line-height: 1.4;
        color: black;
    }

    /* 预览容器打印样式 */
    .preview-section {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        background: white !important;
    }

    #preview-container {
        display: block !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        transform: none !important;
        overflow: visible !important;
    }

    /* A4文档容器打印样式 */
    #document-preview {
        display: block !important;
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 auto !important;
        padding: 0 !important;
        transform: none !important;
        box-shadow: none !important;
        background: white !important;
        page-break-after: always;
    }

    #document-container {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 15mm 20mm !important;
        background: white !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }

    /* 隐藏空白图片占位符 */
    .image-placeholder,
    .header-placeholder,
    .footer-placeholder,
    .stamp-placeholder {
        display: none !important;
    }

    /* 确保图片正确显示 - 保持原始比例打印优化 */
    .document-header-image-container img,
    .company-footer-image-container img,
    .unified-document-footer.company-footer-image-container img {
        object-fit: contain !important; /* 关键修复：保持原始比例，不拉伸变形 */
        object-position: center !important;
        
        /* 强制防止拉伸变形 - 关键修复 */
        min-width: 0 !important;
        min-height: 0 !important;
        flex-shrink: 0 !important;
        
        /* 打印时强制最高质量渲染 - 兼容多浏览器 */
        image-rendering: -webkit-optimize-contrast !important; /* Safari/Chrome/Edge */
        image-rendering: crisp-edges !important; /* Firefox/Chrome */
        image-rendering: high-quality !important; /* 通用 */
        
        /* 防止打印时图片模糊和压缩 */
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        
        /* 确保无损打印 */
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }

    /* 印章图片单独处理 - 保持比例 */
    .company-stamp img {
        object-fit: contain !important; /* 印章也保持比例 */
        object-position: center !important;
        opacity: 0.9 !important;
        
        /* 防拉伸变形属性 */
        min-width: 0 !important;
        min-height: 0 !important;
        flex-shrink: 0 !important;
        
        /* 打印时强制最高质量渲染 */
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: crisp-edges !important;
        image-rendering: high-quality !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 印章容器打印样式 - 防裁切 */
    .company-stamp {
        overflow: visible !important; /* 关键：防止印章被裁切 */
        position: absolute !important;
        bottom: 15% !important;
        right: calc(5% + 25px) !important;
        z-index: 30 !important;
        width: 96px !important;
        height: 96px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
    }

    /* 页眉图片打印专用 - 保持比例 */
    .document-header-image-container img {
        height: 150px !important; /* 固定高度150px */
        width: auto !important; /* 宽度自动计算，保持原始宽高比 */
        max-width: 100% !important; /* 限制最大宽度不超过容器 */
        object-fit: contain !important; /* 保持原始比例，不拉伸 */
        object-position: center !important; /* 居中对齐 */
    }

    /* 页脚图片打印专用 - 保持比例 */
    .unified-document-footer.company-footer-image-container img {
        height: 100% !important; /* 完全填充110px高度的页脚容器 */
        width: auto !important; /* 宽度自动计算，保持比例 */
        max-width: 100% !important; /* 限制最大宽度不超过容器 */
        object-fit: contain !important; /* 保持原始比例，不拉伸 */
        object-position: center !important; /* 居中对齐 */
    }

    /* 总金额样式优化 */
    .total-amount-container {
        background: white !important;
        color: #1e40af !important;
        border: 1px solid #1e40af !important;
        padding: 8px 12px !important;
        margin: 10px 0 !important;
        font-weight: bold !important;
        z-index: 200 !important;
        /* 应用打印边距 */
        margin-left: 15px !important;
        margin-right: 15px !important;
    }

    /* 项目表格打印样式 */
    .items-table {
        width: calc(100% - 30px) !important;
        margin-left: 15px !important;
        margin-right: 15px !important;
        border-collapse: collapse !important;
        margin-top: 10px !important;
        margin-bottom: 15px !important;
        font-size: 11pt !important;
    }

    .items-table th,
    .items-table td {
        border: 1px solid #333 !important;
        padding: 6px 8px !important;
        text-align: left !important;
    }

    .items-table th {
        background: #f5f5f5 !important;
        font-weight: bold !important;
    }

    /* 内容区域打印边距 */
    .company-info,
    .customer-info,
    .payment-info,
    .document-title,
    .notes-section {
        padding-left: 15px !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
    }

    .notes-section {
        margin-left: 15px !important;
        margin-right: 15px !important;
        width: calc(100% - 30px) !important;
    }

    /* 分页控制 */
    .page-break {
        page-break-before: always;
    }

    .no-page-break {
        page-break-inside: avoid;
    }

    /* 页眉页脚打印样式 */
    .document-header,
    .document-header-image-container {
        position: relative !important;
        width: 100% !important;
        height: 150px !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        background: white !important; /* 确保白色背景 */
        box-shadow: none !important; /* 移除阴影 */
        border: none !important; /* 移除边框 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: hidden !important;
    }

    /* 打印时确保页眉容器没有灰色背景 */
    .document-header {
        background-color: white !important;
        background-image: none !important;
    }

    .document-footer,
    .unified-document-footer {
        position: absolute !important;
        bottom: 10mm !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        height: 110px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: white !important;
    }

    /* 打印时隐藏调试元素 */
    #preview-container::before,
    #document-preview::after {
        display: none !important;
    }

    /* 打印颜色保持 */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* 打印字体优化 */
    body, * {
        font-family: 'Times New Roman', 'SimSun', serif !important;
    }

    /* 打印时禁用动画和过渡 */
    *, *::before, *::after {
        animation: none !important;
        transition: none !important;
    }
}
/* #endregion */
