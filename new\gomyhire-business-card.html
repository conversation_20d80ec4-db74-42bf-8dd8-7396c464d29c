<!--
@file gomyhire-business-card.html - GoMyHire 名片 1:1 复刻页面
@description 复刻用户提供名片的设计，以 HTML + CSS 方式实现 1:1 等比例展示
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GoMyHire 名片</title>
  <!-- 使用 Google Fonts 的 Montserrat 字体族，尽量还原排版 -->
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap" rel="stylesheet" />
  <style>
    /*
    #region 全局样式  全局重置与排版
    */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: #f0f0f0; /* 灰色背景模拟拍摄环境 */
      font-family: "Montserrat", "Microsoft YaHei", sans-serif;
    }
    /*
    #endregion
    */

    /*
    #region 名片主体容器
    */
    .gmh-card {
      /* 标准名片尺寸：90mm × 54mm，精确比例 */
      width: 1000px;
      height: 600px;
      position: relative;
      background: #8a2c94; /* 更精确的紫色调匹配原图 */
      color: #ffffff;
      overflow: hidden;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
      border-radius: 6px; /* 轻微圆角 */
    }
    /*
    #endregion
    */

    /*
    #region 右上角白色斜切区域与 LOGO
    */
    .gmh-card__corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      clip-path: polygon(65% 0%, 100% 0%, 100% 45%);
      pointer-events: none;
    }
    .gmh-card__logo {
      position: absolute;
      top: 50px;
      right: 60px;
      color: #5a5a5a;
      font-weight: 700;
      font-size: 52px;
      line-height: 1;
      white-space: nowrap;
      z-index: 2;
    }
    .gmh-card__logo sup {
      font-size: 26px;
      vertical-align: super;
    }
    /*
    #endregion
    */

    /*
    #region 左侧主要信息区域
    */
    .gmh-card__info {
      position: absolute;
      top: 60px;
      left: 60px;
      z-index: 1;
    }
    .gmh-card__info .name {
      font-size: 54px;
      font-weight: 700;
      line-height: 1.1;
      margin-bottom: 10px;
    }
    .gmh-card__info .cn-name {
      margin-left: 18px;
      font-weight: 400;
    }
    .gmh-card__info .title {
      font-size: 32px;
      font-weight: 500;
      line-height: 1.2;
      margin-top: 18px;
      margin-bottom: 30px;
      max-width: 450px;
    }
    .gmh-card__info .contact {
      list-style: none;
      font-size: 32px;
      font-weight: 400;
    }
    .gmh-card__info .contact li {
      display: flex;
      align-items: center;
      margin: 10px 0;
    }
    .gmh-card__info .contact .icon {
      width: 32px;
      height: 32px;
      margin-right: 18px;
      display: inline-block;
      text-align: center;
      font-size: 24px;
    }
    /*
    #endregion
    */

    /*
    #region 二维码区域
    */
    .gmh-card__qr {
      position: absolute;
      top: 180px;
      right: 240px;
      width: 200px;
      height: 200px;
      background: #ffffff;
      padding: 18px;
      z-index: 1;
    }
    .gmh-card__qr img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    /*
    #endregion
    */

    /*
    #region 底部分割线与联系信息
    */
    .gmh-card__separator {
      position: absolute;
      left: 60px;
      right: 60px;
      bottom: 140px;
      height: 1px;
      background: rgba(255, 255, 255, 0.9);
    }
    .gmh-card__footer {
      position: absolute;
      left: 60px;
      right: 60px;
      bottom: 35px;
      display: grid;
      grid-template-columns: 1fr 1.3fr 1.3fr;
      gap: 35px;
      font-size: 22px;
      line-height: 1.4;
    }
    .gmh-card__footer ul {
      list-style: none;
    }
    .gmh-card__footer li {
      display: flex;
      align-items: flex-start;
      margin: 5px 0;
      font-weight: 400;
    }
    .gmh-card__footer .icon {
      width: 22px;
      height: 22px;
      margin-right: 10px;
      display: inline-block;
      text-align: center;
      font-size: 16px;
      flex-shrink: 0;
      margin-top: 2px;
    }
    .gmh-card__footer .office-title {
      font-weight: 700;
      margin-bottom: 3px;
    }
    /*
    #endregion
    */
  </style>
</head>
<body>
  <!-- 名片核心容器 -->
  <section class="gmh-card">
    <!-- 右上角白色三角形背景 -->
    <div class="gmh-card__corner"></div>

    <!-- LOGO 文字 -->
    <h1 class="gmh-card__logo">GoMyHire<sup>™</sup></h1>

    <!-- 左侧主要信息 -->
    <div class="gmh-card__info">
      <div class="name">JC YAP <span class="cn-name">叶俊权</span></div>
      <div class="title">OTA BUSINESS DEVELOPMENT &<br />OPERATION OFFICER</div>

      <ul class="contact">
        <li>
          <span class="icon">📱</span>+6017-326 8882
        </li>
        <li>
          <span class="icon">✉️</span><EMAIL>
        </li>
        <li>
          <span class="icon">💬</span>jcyap890427
        </li>
      </ul>
    </div>

    <!-- 二维码 -->
    <div class="gmh-card__qr">
      <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxNTAiIGhlaWdodD0iMTUwIiBmaWxsPSJ3aGl0ZSIvPgo8IS0tIFNpbXBsaWZpZWQgUVIgY29kZSBwYXR0ZXJuIC0tPgo8cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iMTAwIiB5PSIxMCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSIxMCIgeT0iMTAwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIGZpbGw9ImJsYWNrIi8+CjwhLS0gQ2VudGVyIHBhdHRlcm4gLS0+CjxyZWN0IHg9IjYwIiB5PSI2MCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjMwIiBmaWxsPSJibGFjayIvPgo8IS0tIERvdCBwYXR0ZXJuIC0tPgo8cmVjdCB4PSIyMCIgeT0iNjAiIHdpZHRoPSI1IiBoZWlnaHQ9IjUiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjMwIiB5PSI3MCIgd2lkdGg9IjUiIGhlaWdodD0iNSIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iNDAiIHk9IjIwIiB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSJibGFjayIvPgo8cmVjdCB4PSI3MCIgeT0iMzAiIHdpZHRoPSI1IiBoZWlnaHQ9IjUiIGZpbGw9ImJsYWNrIi8+CjxyZWN0IHg9IjgwIiB5PSI4MCIgd2lkdGg9IjUiIGhlaWdodD0iNSIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iMTIwIiB5PSI2MCIgd2lkdGg9IjUiIGhlaWdodD0iNSIgZmlsbD0iYmxhY2siLz4KPHJlY3QgeD0iNjAiIHk9IjEyMCIgd2lkdGg9IjUiIGhlaWdodD0iNSIgZmlsbD0iYmxhY2siLz4KPC9zdmc+" alt="WhatsApp QR Code" />
    </div>

    <!-- 底部分割线 -->
    <div class="gmh-card__separator"></div>

    <!-- 底部三列信息 -->
    <div class="gmh-card__footer">
      <!-- 左列：联系方式 -->
      <ul>
        <li><span class="icon">📞</span>03-78900670</li>
        <li><span class="icon">📱</span>016-223 4711</li>
        <li><span class="icon">✉️</span><EMAIL></li>
        <li><span class="icon">🌐</span>www.gomyhire.com.my</li>
      </ul>

      <!-- 中列：总部地址 -->
      <ul>
        <li>
          <span class="icon">📍</span>
          <div>
            <div class="office-title">HQ OFFICE</div>
            <div>20-01, Menara Teras,<br />Empire City, 47820<br />Petaling Jaya Selangor<br />Malaysia</div>
          </div>
        </li>
      </ul>

      <!-- 右列：分公司地址 -->
      <ul>
        <li>
          <span class="icon">🏢</span>
          <div>
            <div class="office-title">BRANCH OFFICE</div>
            <div>Kota Kinabalu Sabah, Malaysia<br />Chin Swee Road, Singapore</div>
          </div>
        </li>
      </ul>
    </div>
  </section>
</body>
</html> 