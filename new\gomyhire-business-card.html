<!--
@file gomyhire-business-card.html - GoMyHire 名片 1:1 复刻页面
@description 复刻用户提供名片的设计，以 HTML + CSS 方式实现 1:1 等比例展示
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>GoMyHire 名片</title>
  <!-- 使用 Google Fonts 的 Montserrat 字体族，尽量还原排版 -->
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap" rel="stylesheet" />
  <style>
    /*
    #region 全局样式  全局重置与排版
    */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: #f0f0f0; /* 灰色背景模拟拍摄环境 */
      font-family: "Montserrat", "Microsoft YaHei", sans-serif;
    }
    /*
    #endregion
    */

    /*
    #region 名片主体容器
    */
    .gmh-card {
      /* 标准名片尺寸：90mm × 54mm，最终精确版 */
      width: 1000px;
      height: 540px; /* 调整为更精确的 90:54 比例 */
      position: relative;
      background: #8A2C94; /* 最终颜色校准 */
      color: #ffffff;
      overflow: hidden;
      box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
      border-radius: 4px; /* 更贴近原图的细微圆角 */
    }
    /*
    #endregion
    */

    /*
    #region 右上角白色斜切区域与 LOGO
    */
    .gmh-card__corner {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      clip-path: polygon(62% 0, 100% 0, 100% 55%); /* 最终角度校准 */
      pointer-events: none;
    }
    .gmh-card__logo {
      position: absolute;
      top: 40px;
      right: 50px;
      color: #444444; /* 最终颜色校准 */
      font-weight: 700;
      font-size: 46px; /* 最终大小校准 */
      letter-spacing: -1px;
      white-space: nowrap;
      z-index: 2;
    }
    .gmh-card__logo sup {
      font-size: 23px;
    }
    /*
    #endregion
    */

    /*
    #region 左侧主要信息区域
    */
    .gmh-card__info {
      position: absolute;
      top: 55px;
      left: 55px;
      z-index: 1;
    }
    .gmh-card__info .name {
      font-size: 48px;
      font-weight: 700;
      line-height: 1.1;
      margin-bottom: 12px;
    }
    .gmh-card__info .cn-name {
      margin-left: 15px;
      font-weight: 500;
    }
    .gmh-card__info .title {
      font-size: 28px;
      font-weight: 500;
      line-height: 1.25;
      margin-top: 15px;
      margin-bottom: 30px;
      max-width: 450px;
      letter-spacing: 0.5px;
    }
    .gmh-card__info .contact {
      list-style: none;
      font-size: 28px;
      font-weight: 500;
    }
    .gmh-card__info .contact li {
      display: flex;
      align-items: center;
      margin: 12px 0;
    }
    .gmh-card__info .contact .icon {
      width: 28px;
      height: 28px;
      margin-right: 15px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
    .gmh-card__info .contact .icon svg {
      width: 100%;
      height: 100%;
      fill: #ffffff;
    }
    /*
    #endregion
    */

    /*
    #region 二维码区域
    */
    .gmh-card__qr {
      position: absolute;
      top: 170px;
      right: 170px;
      width: 200px;
      height: 200px;
      background: #ffffff;
      padding: 12px;
      z-index: 1;
    }
    .gmh-card__qr img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    /*
    #endregion
    */

    /*
    #region 底部分割线与联系信息
    */
    .gmh-card__separator {
      position: absolute;
      left: 55px;
      right: 55px;
      bottom: 150px;
      height: 1.5px;
      background: rgba(255, 255, 255, 0.7);
    }
    .gmh-card__footer {
      position: absolute;
      left: 55px;
      right: 55px;
      bottom: 25px;
      display: grid;
      grid-template-columns: 1fr 1.2fr 1.2fr;
      gap: 30px;
      font-size: 19px;
      line-height: 1.35;
    }
    .gmh-card__footer ul {
      list-style: none;
    }
    .gmh-card__footer li {
      display: flex;
      align-items: flex-start;
      margin: 4px 0;
      font-weight: 500;
    }
    .gmh-card__footer .icon {
      width: 19px;
      height: 19px;
      margin-right: 10px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: 2px;
    }
     .gmh-card__footer .icon svg {
      width: 100%;
      height: 100%;
      fill: #ffffff;
    }
    .gmh-card__footer .office-title {
      font-weight: 700;
      margin-bottom: 4px;
      display: inline-flex;
      align-items: center;
    }
    .gmh-card__footer .office-title .icon {
      margin-right: 8px;
    }
    /*
    #endregion
    */
  </style>
</head>
<body>
  <!-- 名片核心容器 -->
  <section class="gmh-card">
    <!-- 右上角白色三角形背景 -->
    <div class="gmh-card__corner"></div>

    <!-- LOGO 文字 -->
    <h1 class="gmh-card__logo">GoMyHire<sup>™</sup></h1>

    <!-- 左侧主要信息 -->
    <div class="gmh-card__info">
      <div class="name">JC YAP <span class="cn-name">叶俊权</span></div>
      <div class="title">OTA BUSINESS DEVELOPMENT &amp;<br />OPERATION OFFICER</div>

      <ul class="contact">
        <li>
          <span class="icon"><svg viewBox="0 0 24 24"><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/></svg></span>+6017-326 8882
        </li>
        <li>
          <span class="icon"><svg viewBox="0 0 24 24"><path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/></svg></span><EMAIL>
        </li>
        <li>
          <span class="icon"><svg viewBox="0 0 24 24"><path d="M12,2C15.86,2 19,5.13 19,9C19,14.25 12,22 12,22C12,22 5,14.25 5,9C5,5.13 8.13,2 12,2M12,4A5,5 0 0,0 7,9C7,10.31 7.73,12.21 9,14.33C10.23,16.4 11.3,17.94 12,19.23C12.7,17.94 13.77,16.4 15,14.33C16.27,12.21 17,10.31 17,9A5,5 0 0,0 12,4M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/></svg></span>jcyap890427
        </li>
      </ul>
    </div>

    <!-- 二维码 -->
    <div class="gmh-card__qr">
      <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADIAQMAAACXljzdAAAABlBMVEX///8AAABVwtN+AAACsElEQVR42uyYQW7kQAwEASsKyOEG1mCTHEo3YJNVsIfuUo4gVR4gwYUkK+KFLMkl+f24YgKzWk+Pl/ba2uKzLq68D0+2j6+5fN7W93N/T69t+w9A/gGQvwDkLwD5C0D+ApC/AOUvAPkLQP4CkL8A5S8A+QtA/gKQvwDlB0B+A8hvAPkNIH8ByG8A8htA/gKQ3wDyG0B+A8g/APkHQP4BkH8A5B8A+QdA/gGQfwDkHwD5G0D+BpC/AeRvAPkbQP4GkL8B5G8A+RtA/hMQ/oSEPyHhT0j4ExL+hIQ/IeFPSPgTEv6EhD8h4U9I+BMS/oSEPyHhT0j4EwL+hIA/IeBPCPhTAf4EgT8h4E8I+BPCf5sA/yYF/JkE/EyCPxPgz6T5Myn+zIQ/k+HPJPgzaf6MhD8j4c+I+DMh/oyEPyPhz4j4Myn+jIQ/o+DPSPgzC/6MhD8z4c8s+DML/sxMf2bCn5nwZyb8mQl/ZsafmZJ/ZsqfmfJnkvyZKX9myn9Gwf+Mgn9GwZ+R8Gck/BkJfyYn/JkUfyYl/JkUfyYl/JkUf0bCn1HwZxT8GQV/RsGfUfBnVPwZFX9Gxc/E+DMR/kyMPxPhz0T4MxH+zCo/s4pP/MwLP/M6z/zMLz/znm9+5oFvfqbf/Mwr3/xMrvnhO5LcA8lvAPkNIL8B5DeA/AZQfyrkt0L+VshvhfxWSG+F9FZIb4X0VkhvhfRWSC+F9FZIb4X0VkhvhfRWSC+F9FZIb4X0VkhvhfRWSC+F9FZIb4X0VkhvhfRWSC+F9FZIb4X0VkhvhfRWSC+F9FZIb4X0VkhvhfRWSG+F9FZIbwD5DSD/AMg/APIPgPwDIP8AyD8A8g+A/A0gf9fQ/gL5v1L+ApC/AOUvAPkLQP4CkL8A5S8A+QtA/gKQv6D9BaB/zJd9AfkXQNYo4e+dAAAAAElFTkSuQmCC" alt="WhatsApp QR Code" />
    </div>

    <!-- 底部分割线 -->
    <div class="gmh-card__separator"></div>

    <!-- 底部三列信息 -->
    <div class="gmh-card__footer">
      <!-- 左列：联系方式 -->
      <ul>
        <li><span class="icon"><svg viewBox="0 0 24 24"><path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/></svg></span>03-78900670</li>
        <li><span class="icon"><svg viewBox="0 0 24 24"><path d="M17,17H7V15H17M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z"/></svg></span>016-223 4711</li>
        <li><span class="icon"><svg viewBox="0 0 24 24"><path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/></svg></span><EMAIL></li>
        <li><span class="icon"><svg viewBox="0 0 24 24"><path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,19.3L12.9,18.1C12.4,17.9 12,18.1 11.8,18.5L11.1,20C8.3,18.6 6.4,16.7 5,13.9L6.5,13.2C6.9,13 7.1,12.6 6.9,12.1L5.7,8.8C5.5,8.3 5.1,8 4.6,8H3C2.9,8 2.8,8.1 2.8,8.2C2.5,9.5 2.2,12 3.8,14.8C5.4,17.6 8.4,19.5 11.8,19.8C11.9,19.8 12,19.7 12,19.6V17.9C12,17.4 12.4,17 12.9,17.2L16.2,18.4C16.7,18.6 17,19 16.8,19.5L16.2,19.3Z"/></svg></span>www.gomyhire.com.my</li>
      </ul>

      <!-- 中列：总部地址 -->
      <ul>
        <li>
          <div class="office-title">
            <span class="icon"><svg viewBox="0 0 24 24"><path d="M12,2C15.86,2 19,5.13 19,9C19,14.25 12,22 12,22C12,22 5,14.25 5,9C5,5.13 8.13,2 12,2M12,4A5,5 0 0,0 7,9C7,10.31 7.73,12.21 9,14.33C10.23,16.4 11.3,17.94 12,19.23C12.7,17.94 13.77,16.4 15,14.33C16.27,12.21 17,10.31 17,9A5,5 0 0,0 12,4M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/></svg></span>HQ OFFICE
          </div>
          <div>20-01, Menara Teras,<br />Empire City, 47820<br />Petaling Jaya Selangor<br />Malaysia</div>
        </li>
      </ul>

      <!-- 右列：分公司地址 -->
      <ul>
        <li>
          <div class="office-title">
            <span class="icon"><svg viewBox="0 0 24 24"><path d="M18,16V14.5C18,13.12 15.33,12.5 12,12.5C8.67,12.5 6,13.12 6,14.5V16H18M15,9A3,3 0 0,1 12,12A3,3 0 0,1 9,9A3,3 0 0,1 12,6A3,3 0 0,1 15,9M12,2C16.42,2 20,5.58 20,10C20,15.05 15.17,21.17 12.83,23.5C12.5,23.83 11.5,23.83 11.17,23.5C8.83,21.17 4,15.05 4,10C4,5.58 7.58,2 12,2Z"/></svg></span>BRANCH OFFICE
          </div>
          <div>Kota Kinabalu Sabah, Malaysia<br />Chin Swee Road, Singapore</div>
        </li>
      </ul>
    </div>
  </section>
</body>
</html> 