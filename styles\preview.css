/**
 * @file 预览模式样式文件
 * @description 预览模式专用样式，确保图片正确显示和布局
 * @version 5.0
 * @date 2024-12-21
 */

/* #region 预览模式图片样式 - 防拉伸变形核心 */
/* 页眉图片预览样式 - 关键修复 */
.document-header-image-container img {
    /* 保持比例显示 - 防止拉伸变形 */
    height: 100% !important;
    width: auto !important;
    max-width: 100% !important;
    object-fit: contain !important; /* 关键：保持原始比例，不裁剪不拉伸 */
    object-position: center !important;
    margin: 0 auto !important;
    display: block !important;
    
    /* 强制防止拉伸变形 - 关键修复属性 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;
    
    /* 无损高质量渲染 - 兼容多浏览器 */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    image-rendering: high-quality !important;
    
    /* 防止图片模糊和压缩 */
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    
    /* 确保无损显示 */
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* 页脚图片预览样式 */
.company-footer-image-container img,
.unified-document-footer img {
    height: 100% !important;
    width: auto !important;
    max-width: 100% !important;
    object-fit: contain !important;
    object-position: center !important;
    margin: 0 auto !important;
    display: block !important;
    
    /* 防拉伸变形属性 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;
    
    /* 高质量渲染 */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    image-rendering: high-quality !important;
}

/* 印章图片预览样式 - 防裁切 */
.company-stamp {
    /* 关键：防止印章被裁切 */
    overflow: visible !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute;
    bottom: var(--stamp-bottom-offset);
    right: var(--stamp-right-offset);
    z-index: var(--z-index-stamp);
    width: 96px;
    height: 96px;
    box-sizing: border-box;
}

.company-stamp img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    opacity: 0.9 !important;
    
    /* 防拉伸变形属性 */
    min-width: 0 !important;
    min-height: 0 !important;
    flex-shrink: 0 !important;
    
    /* 高质量渲染 */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    image-rendering: high-quality !important;
}
/* #endregion */

/* #region 预览模式布局优化 */
/* 页眉容器预览样式 */
.document-header,
.document-header-image-container {
    position: relative;
    width: 100%;
    height: var(--header-height);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    padding: 5px;
    box-sizing: border-box;
    margin: 0 auto 15px auto;
    overflow: hidden; /* 防止图片超出容器，但不影响图片显示 */
}

/* 页脚容器预览样式 */
.unified-document-footer.company-footer-image-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: auto;
    min-height: var(--footer-height);
    background-color: white;
    z-index: var(--z-index-footer);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    padding: 5px 5px 2px 5px;
    box-sizing: border-box;
    max-width: var(--a4-width-px);
    margin: 0 auto;
}
/* #endregion */

/* #region 预览模式内容样式 */
/* 文档标题预览样式 */
.document-title {
    text-align: center;
    font-size: var(--title-font-size);
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 20px;
    /* 应用内容边距 */
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
}

/* 公司信息预览样式 */
.company-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
    /* 应用内容边距 */
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
}

/* 客户信息预览样式 */
.customer-info {
    margin-bottom: 15px;
    /* 应用内容边距 */
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
}

/* 支付信息预览样式 */
.payment-info {
    margin-bottom: 15px;
    /* 应用内容边距 */
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
}

/* 项目表格预览样式 */
.items-table {
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right));
    margin-left: var(--content-margin-left);
    margin-right: var(--content-margin-right);
    border-collapse: collapse;
    margin-top: 10px;
    margin-bottom: 15px;
}

.items-table th,
.items-table td {
    padding: 8px;
    border: 1px solid var(--border-color);
    text-align: left;
    font-size: var(--base-font-size);
    line-height: var(--line-height);
}

.items-table th {
    background-color: var(--light-color);
    font-weight: 600;
}

/* 总金额容器预览样式 */
.total-amount-container {
    background-color: #ffffff;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 18px;
    margin: 15px var(--content-margin-left);
    border-radius: 6px;
    box-shadow: none;
    z-index: var(--z-index-total);
    position: relative;
    display: inline-block;
    min-width: 200px;
    text-align: center;
}

.total-amount-container h3 {
    margin: 0;
    font-size: 16px;
    font-weight: bold;
    line-height: 1.4;
    color: var(--primary-color);
    text-shadow: none;
    white-space: nowrap;
}

/* 备注区域预览样式 */
.notes-section {
    margin-top: 15px;
    margin-left: var(--content-margin-left);
    margin-right: var(--content-margin-right);
    width: calc(100% - var(--content-margin-left) - var(--content-margin-right));
    padding-left: var(--content-margin-left);
    padding-right: var(--content-margin-right);
    box-sizing: border-box;
    font-size: var(--base-font-size);
    line-height: var(--line-height);
}
/* #endregion */

/* #region 预览模式特殊效果 */
/* 预览状态指示器 */
.preview-status-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    z-index: 1000;
}

.preview-status-indicator.hidden {
    display: none;
}

/* 空预览消息 */
.empty-preview-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
    font-style: italic;
    font-size: 14px;
    z-index: 100;
}

/* 预览容器悬停效果 */
#document-preview:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: box-shadow var(--transition-normal);
}

/* 预览缩放控制 */
.preview-zoom-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    z-index: 1000;
}

.zoom-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background: rgba(30, 64, 175, 0.8);
    color: white;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.zoom-btn:hover {
    background: rgba(30, 64, 175, 1);
    transform: scale(1.1);
}
/* #endregion */

/* #region 预览模式动画 */
/* 内容更新动画 */
.content-updating {
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.content-updated {
    opacity: 1;
    transition: opacity var(--transition-fast);
}

/* 图片加载动画 */
.image-loading {
    position: relative;
}

.image-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 预览刷新动画 */
.preview-refreshing {
    animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
/* #endregion */
